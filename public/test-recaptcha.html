<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reCAPTCHA Test - RELIFE</title>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .recaptcha-container {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }
        .config-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 RELIFE reCAPTCHA Test</h1>
        
        <div class="config-info" id="configInfo">
            Loading configuration...
        </div>

        <form id="contactForm">
            <div class="form-group">
                <label for="name">Full Name *</label>
                <input type="text" id="name" name="name" required>
            </div>

            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone">
            </div>

            <div class="form-group">
                <label for="subject">Subject *</label>
                <select id="subject" name="subject" required>
                    <option value="">Select a subject</option>
                    <option value="general-inquiry">General Inquiry</option>
                    <option value="product-information">Product Information</option>
                    <option value="support">Technical Support</option>
                    <option value="warranty">Warranty Claim</option>
                    <option value="partnership">Partnership Opportunity</option>
                </select>
            </div>

            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" required placeholder="Please describe your inquiry in detail..."></textarea>
            </div>

            <div class="recaptcha-container">
                <div class="g-recaptcha" data-sitekey="SITE_KEY_PLACEHOLDER"></div>
            </div>

            <button type="submit" id="submitBtn">Send Message</button>
        </form>

        <div class="result" id="result"></div>
    </div>

    <script>
        let recaptchaSiteKey = '';

        // Load configuration
        async function loadConfig() {
            try {
                const response = await fetch('/api/v1/config/recaptcha');
                const data = await response.json();
                
                if (data.success) {
                    recaptchaSiteKey = data.data.site_key;
                    document.querySelector('.g-recaptcha').setAttribute('data-sitekey', recaptchaSiteKey);
                    
                    document.getElementById('configInfo').innerHTML = `
                        <strong>reCAPTCHA Configuration:</strong><br>
                        Site Key: ${recaptchaSiteKey}<br>
                        Enabled: ${data.data.enabled ? 'Yes' : 'No'}
                    `;
                } else {
                    throw new Error('Failed to load configuration');
                }
            } catch (error) {
                document.getElementById('configInfo').innerHTML = `
                    <strong>Error loading configuration:</strong> ${error.message}
                `;
            }
        }

        // Handle form submission
        document.getElementById('contactForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const result = document.getElementById('result');
            
            // Get reCAPTCHA response
            const recaptchaResponse = grecaptcha.getResponse();
            
            if (!recaptchaResponse) {
                showResult('Please complete the reCAPTCHA verification.', 'error');
                return;
            }

            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Sending...';

            // Prepare form data
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                subject: document.getElementById('subject').value,
                message: document.getElementById('message').value,
                captcha_token: recaptchaResponse
            };

            try {
                const response = await fetch('/api/v1/contact', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (data.success) {
                    showResult(`✅ Success! ${data.message}`, 'success');
                    document.getElementById('contactForm').reset();
                    grecaptcha.reset();
                } else {
                    let errorMessage = data.message || 'An error occurred';
                    if (data.errors) {
                        const errors = Object.values(data.errors).flat();
                        errorMessage += '<br><br>Details:<br>• ' + errors.join('<br>• ');
                    }
                    showResult(`❌ Error: ${errorMessage}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
            } finally {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Send Message';
            }
        });

        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            // Scroll to result
            result.scrollIntoView({ behavior: 'smooth' });
        }

        // Load configuration on page load
        loadConfig();
    </script>
</body>
</html>
