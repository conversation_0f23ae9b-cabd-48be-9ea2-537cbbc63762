<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Laravel admin routes FIRST (highest priority)
    # This must come before React Router fallback to prevent conflicts
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} ^/admin/?
    RewriteRule ^(.*)$ index.php [L]

    # Handle Laravel API routes
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} ^/api/
    RewriteRule ^(.*)$ index.php [L]

    # Handle React Router SPA routing (fallback for frontend routes)
    # This comes LAST to avoid intercepting Laravel routes
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteCond %{REQUEST_URI} !^/admin
    RewriteCond %{REQUEST_URI} !^/storage/
    RewriteCond %{REQUEST_URI} !^/assets/
    RewriteCond %{REQUEST_URI} !^/css/
    RewriteCond %{REQUEST_URI} !^/js/
    RewriteCond %{REQUEST_URI} !^/images/
    RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf)$
    RewriteRule ^(.*)$ /index.html [L]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Security Headers
    <IfModule mod_headers.c>
        # CORS Headers for API
        Header always set Access-Control-Allow-Origin "https://re-life.ca"
        Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-CSRF-TOKEN, X-XSRF-TOKEN"
        Header always set Access-Control-Allow-Credentials "true"

        # Security Headers
        Header always set X-Content-Type-Options "nosniff"
        Header always set X-Frame-Options "SAMEORIGIN"
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
        Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
        
        # HSTS (HTTP Strict Transport Security)
        Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    </IfModule>

    # Compression
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/json
    </IfModule>

    # Browser Caching
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType text/css "access plus 1 year"
        ExpiresByType application/javascript "access plus 1 year"
        ExpiresByType image/png "access plus 1 year"
        ExpiresByType image/jpg "access plus 1 year"
        ExpiresByType image/jpeg "access plus 1 year"
        ExpiresByType image/gif "access plus 1 year"
        ExpiresByType image/svg+xml "access plus 1 year"
        ExpiresByType font/woff "access plus 1 year"
        ExpiresByType font/woff2 "access plus 1 year"
        ExpiresByType application/pdf "access plus 1 month"
        ExpiresByType text/html "access plus 1 hour"
    </IfModule>

    # File Security
    <Files ".env">
        Order allow,deny
        Deny from all
    </Files>

    <Files ".env.*">
        Order allow,deny
        Deny from all
    </Files>

    <Files "composer.json">
        Order allow,deny
        Deny from all
    </Files>

    <Files "composer.lock">
        Order allow,deny
        Deny from all
    </Files>

    <Files "package.json">
        Order allow,deny
        Deny from all
    </Files>

    <Files "package-lock.json">
        Order allow,deny
        Deny from all
    </Files>

    # Prevent access to sensitive directories
    RedirectMatch 404 /\.git
    RedirectMatch 404 /\.env
    RedirectMatch 404 /composer\.
    RedirectMatch 404 /package\.
    RedirectMatch 404 /node_modules
    RedirectMatch 404 /storage/logs
</IfModule>
