<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->text('features')->nullable(); // JSON field for features list
            $table->text('specifications')->nullable(); // JSON field for technical specs
            $table->decimal('price', 10, 2)->nullable();
            $table->string('sku')->unique()->nullable();
            $table->string('model_number')->nullable();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->string('status')->default('active'); // active, inactive, discontinued
            $table->date('release_date')->nullable();
            $table->text('warranty_info')->nullable();
            $table->text('compatibility')->nullable(); // JSON field for compatibility info
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
