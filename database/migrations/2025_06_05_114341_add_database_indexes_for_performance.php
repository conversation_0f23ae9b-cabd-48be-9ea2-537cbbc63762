<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Products table indexes
        Schema::table('products', function (Blueprint $table) {
            $table->index(['is_active', 'is_featured']); // For filtering active/featured products
            $table->index(['category_id', 'is_active']); // For category filtering
            $table->index(['status', 'is_active']); // For status filtering
            $table->index(['created_at', 'is_active']); // For date-based queries
            $table->index('sort_order'); // For ordering
            $table->index('price'); // For price filtering
        });

        // Categories table indexes
        Schema::table('categories', function (Blueprint $table) {
            $table->index(['is_active', 'sort_order']); // For active categories ordering
        });

        // Catalogs table indexes
        Schema::table('catalogs', function (Blueprint $table) {
            $table->index(['is_active', 'category']); // For filtering by category and status
            $table->index(['published_date', 'is_active']); // For date-based filtering
            $table->index('download_count'); // For popularity sorting
            $table->index('sort_order'); // For ordering
        });

        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            $table->index('is_admin'); // For admin filtering
            $table->index('sort_order'); // For ordering
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['is_active', 'is_featured']);
            $table->dropIndex(['category_id', 'is_active']);
            $table->dropIndex(['status', 'is_active']);
            $table->dropIndex(['created_at', 'is_active']);
            $table->dropIndex(['sort_order']);
            $table->dropIndex(['price']);
        });

        Schema::table('categories', function (Blueprint $table) {
            $table->dropIndex(['is_active', 'sort_order']);
        });

        Schema::table('catalogs', function (Blueprint $table) {
            $table->dropIndex(['is_active', 'category']);
            $table->dropIndex(['published_date', 'is_active']);
            $table->dropIndex(['download_count']);
            $table->dropIndex(['sort_order']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['is_admin']);
            $table->dropIndex(['sort_order']);
        });
    }
};
