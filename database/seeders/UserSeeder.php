<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Generate a secure random password for admin user
        $adminPassword = env('ADMIN_DEFAULT_PASSWORD', Str::random(16));

        // Create the admin user for RELIFE INC
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'RELIFE Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make($adminPassword),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]
        );

        if ($adminUser->wasRecentlyCreated) {
            $this->command->info('✅ Admin user created successfully!');
            $this->command->info('📧 Email: <EMAIL>');
            $this->command->info('🔑 Password: ' . $adminPassword);
            $this->command->warn('⚠️  IMPORTANT: Save this password securely and change it after first login!');
            $this->command->warn('⚠️  Set ADMIN_DEFAULT_PASSWORD in .env for consistent password generation.');
        } else {
            $this->command->info('ℹ️  Admin user already exists.');
        }
    }
}
