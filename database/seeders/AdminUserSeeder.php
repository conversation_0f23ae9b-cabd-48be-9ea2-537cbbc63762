<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AdminUserSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->warn('⚠️  AdminUserSeeder is deprecated. Use UserSeeder instead.');
        $this->command->info('ℹ️  This seeder has been disabled for security reasons.');
        $this->command->info('ℹ️  Run: php artisan db:seed --class=UserSeeder');

        // This seeder is deprecated and disabled for security reasons
        // Use UserSeeder instead which implements secure password generation
        return;
    }
}
