<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;

class ProductImageSeeder extends Seeder
{
    public function run(): void
    {
        // Get categories
        $upperLimbCategory = Category::where('slug', 'upper-limb-prosthetics')->first();
        $lowerLimbCategory = Category::where('slug', 'lower-limb-prosthetics')->first();
        $pediatricCategory = Category::where('slug', 'pediatric-prosthetics')->first();
        $sportsCategory = Category::where('slug', 'sports-recreation')->first();
        $accessoriesCategory = Category::where('slug', 'accessories-components')->first();

        // Enhanced products with local images
        $products = [
            // Upper Limb Products
            [
                'name' => 'RELIFE Pro Myoelectric Hand',
                'slug' => 'relife-pro-myoelectric-hand',
                'description' => 'Advanced myoelectric prosthetic hand with individual finger control and natural grip patterns. Features state-of-the-art sensors and intuitive control systems for seamless integration with daily activities.',
                'short_description' => 'State-of-the-art myoelectric hand with precision control.',
                'features' => [
                    'Individual finger control',
                    'Multiple grip patterns',
                    'Waterproof design',
                    'Long battery life (8+ hours)',
                    'Sensory feedback system',
                    'Bluetooth connectivity',
                    'Mobile app control'
                ],
                'specifications' => [
                    'Weight' => '450g',
                    'Battery Life' => '8-12 hours',
                    'Grip Force' => '100N',
                    'Control' => 'EMG signals',
                    'Material' => 'Carbon fiber composite',
                    'Waterproof Rating' => 'IP67',
                    'Operating Temperature' => '-10°C to 50°C'
                ],
                'price' => 25000.00,
                'sku' => 'RL-UL-001',
                'model_number' => 'RLMH-2024',
                'category_id' => $upperLimbCategory->id,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 1,
                'status' => 'active',
                'warranty_info' => '2-year comprehensive warranty with 24/7 support',
                'images' => ['/product1.jpg', '/product2.jpg']
            ],
            [
                'name' => 'RELIFE Flex Prosthetic Arm',
                'slug' => 'relife-flex-prosthetic-arm',
                'description' => 'Lightweight and flexible prosthetic arm designed for maximum comfort and natural movement. Perfect for daily activities and professional use.',
                'short_description' => 'Lightweight prosthetic arm with natural movement.',
                'features' => [
                    'Lightweight carbon fiber construction',
                    'Natural joint movement',
                    'Quick-release mechanism',
                    'Adjustable grip strength',
                    'Sweat-resistant materials'
                ],
                'specifications' => [
                    'Weight' => '680g',
                    'Material' => 'Carbon fiber and titanium',
                    'Load Capacity' => '15kg',
                    'Range of Motion' => '140° elbow flexion',
                    'Attachment' => 'Locking liner system'
                ],
                'price' => 18000.00,
                'sku' => 'RL-UL-003',
                'model_number' => 'RLFA-2024',
                'category_id' => $upperLimbCategory->id,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 2,
                'status' => 'active',
                'warranty_info' => '2-year warranty with maintenance support',
                'images' => ['/product2.jpg', '/product3.jpg']
            ],
            // Lower Limb Products
            [
                'name' => 'RELIFE Smart Knee System',
                'slug' => 'relife-smart-knee-system',
                'description' => 'Intelligent microprocessor-controlled knee system with adaptive learning algorithms that adjust to your walking patterns for optimal performance.',
                'short_description' => 'AI-powered knee system with adaptive learning.',
                'features' => [
                    'Microprocessor control',
                    'Adaptive learning AI',
                    'Real-time gait analysis',
                    'Weather-resistant design',
                    'Mobile app integration',
                    'Fall detection system'
                ],
                'specifications' => [
                    'Weight' => '1.2kg',
                    'Battery Life' => '24-48 hours',
                    'Max User Weight' => '125kg',
                    'Flexion Angle' => '165°',
                    'Control System' => 'Microprocessor with gyroscope',
                    'Charging Time' => '3 hours'
                ],
                'price' => 45000.00,
                'sku' => 'RL-LL-003',
                'model_number' => 'RLSK-2024',
                'category_id' => $lowerLimbCategory->id,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 1,
                'status' => 'active',
                'warranty_info' => '3-year comprehensive warranty with software updates',
                'images' => ['/product3.jpg', '/product4.jpg']
            ],
            [
                'name' => 'RELIFE Dynamic Foot',
                'slug' => 'relife-dynamic-foot',
                'description' => 'Energy-storing prosthetic foot with carbon fiber spring technology for natural gait and enhanced performance in various activities.',
                'short_description' => 'Energy-storing foot with carbon fiber technology.',
                'features' => [
                    'Carbon fiber spring system',
                    'Energy return technology',
                    'Multi-terrain adaptation',
                    'Lightweight design',
                    'Heel-to-toe transition'
                ],
                'specifications' => [
                    'Weight' => '420g',
                    'Material' => 'Carbon fiber composite',
                    'Heel Height' => '10-15mm adjustable',
                    'Max User Weight' => '100kg',
                    'Activity Level' => 'K3-K4'
                ],
                'price' => 8500.00,
                'sku' => 'RL-LL-004',
                'model_number' => 'RLDF-2024',
                'category_id' => $lowerLimbCategory->id,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 2,
                'status' => 'active',
                'warranty_info' => '1-year warranty with performance guarantee',
                'images' => ['/product4.jpg', '/product1.jpg']
            ],
            // Pediatric Products
            [
                'name' => 'RELIFE Kids Growth Arm',
                'slug' => 'relife-kids-growth-arm',
                'description' => 'Specially designed prosthetic arm for children with growth-accommodating features and fun, colorful designs to encourage active use.',
                'short_description' => 'Growth-accommodating prosthetic arm for children.',
                'features' => [
                    'Growth accommodation system',
                    'Colorful design options',
                    'Lightweight construction',
                    'Easy maintenance',
                    'Child-friendly controls',
                    'Waterproof for play'
                ],
                'specifications' => [
                    'Weight' => '280g',
                    'Age Range' => '5-15 years',
                    'Growth Range' => '5cm adjustment',
                    'Material' => 'Medical grade plastic',
                    'Colors Available' => '8 color options'
                ],
                'price' => 12000.00,
                'sku' => 'RL-PD-003',
                'model_number' => 'RLKG-2024',
                'category_id' => $pediatricCategory->id,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 1,
                'status' => 'active',
                'warranty_info' => '2-year warranty with growth adjustments included',
                'images' => ['/product1.jpg', '/product3.jpg']
            ],
            // Sports & Recreation
            [
                'name' => 'RELIFE Athletic Blade',
                'slug' => 'relife-athletic-blade',
                'description' => 'High-performance running blade designed for competitive athletes. Engineered for speed, agility, and optimal energy return.',
                'short_description' => 'Professional running blade for competitive athletics.',
                'features' => [
                    'Competition-grade carbon fiber',
                    'Optimized energy return',
                    'Aerodynamic design',
                    'Customizable stiffness',
                    'Professional athlete tested'
                ],
                'specifications' => [
                    'Weight' => '350g',
                    'Material' => 'T700 Carbon Fiber',
                    'Length Options' => '5 sizes available',
                    'Stiffness Categories' => '3 levels',
                    'Max Speed' => 'Unlimited'
                ],
                'price' => 15000.00,
                'sku' => 'RL-SP-003',
                'model_number' => 'RLAB-2024',
                'category_id' => $sportsCategory->id,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 1,
                'status' => 'active',
                'warranty_info' => '1-year performance warranty',
                'images' => ['/product2.jpg', '/product4.jpg']
            ]
        ];

        foreach ($products as $productData) {
            $images = $productData['images'];
            unset($productData['images']);
            
            // Create or update product
            $product = Product::updateOrCreate(
                ['slug' => $productData['slug']],
                $productData
            );

            // Add images using Spatie Media Library
            foreach ($images as $imagePath) {
                // Try backend public path first
                $backendPath = public_path($imagePath);
                // Try frontend public path
                $frontendPath = base_path('../public' . $imagePath);

                $fullPath = null;
                if (file_exists($backendPath)) {
                    $fullPath = $backendPath;
                } elseif (file_exists($frontendPath)) {
                    $fullPath = $frontendPath;
                }

                if ($fullPath && file_exists($fullPath)) {
                    try {
                        $product->addMedia($fullPath)
                            ->preservingOriginal()
                            ->toMediaCollection('product-images');
                    } catch (\Exception $e) {
                        echo "Failed to add image {$imagePath}: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
    }
}
