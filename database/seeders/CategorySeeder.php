<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Upper Limb Prosthetics',
                'slug' => 'upper-limb-prosthetics',
                'description' => 'Advanced upper limb prosthetic solutions including myoelectric arms, hands, and specialized attachments.',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Lower Limb Prosthetics',
                'slug' => 'lower-limb-prosthetics',
                'description' => 'Comprehensive lower limb prosthetic systems including microprocessor knees, feet, and ankle systems.',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Pediatric Prosthetics',
                'slug' => 'pediatric-prosthetics',
                'description' => 'Specialized prosthetic solutions designed for children with growth-accommodating features.',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Sports & Recreation',
                'slug' => 'sports-recreation',
                'description' => 'High-performance prosthetic solutions designed for sports and recreational activities.',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Accessories & Components',
                'slug' => 'accessories-components',
                'description' => 'Essential accessories, components, and maintenance items for prosthetic devices.',
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
