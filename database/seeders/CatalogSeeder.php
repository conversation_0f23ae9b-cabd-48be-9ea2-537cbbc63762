<?php

namespace Database\Seeders;

use App\Models\Catalog;
use Illuminate\Database\Seeder;

class CatalogSeeder extends Seeder
{
    public function run(): void
    {
        $catalogs = [
            [
                'name' => 'Complete Product Catalog 2024',
                'slug' => 'complete-product-catalog-2024',
                'description' => 'Comprehensive overview of all our prosthetic solutions including specifications, features, and compatibility information.',
                'category' => 'General',
                'file_path' => 'catalogs/complete-catalog-2024.pdf',
                'file_size' => '12.5 MB',
                'file_type' => 'pdf',
                'is_active' => true,
                'published_date' => now(),
                'version' => '2024.1',
            ],
            [
                'name' => 'Upper Limb Solutions',
                'slug' => 'upper-limb-solutions',
                'description' => 'Detailed catalog focusing on upper limb prosthetics, myoelectric systems, and hand prosthetics.',
                'category' => 'Upper Limb',
                'file_path' => 'catalogs/upper-limb-catalog.pdf',
                'file_size' => '8.2 MB',
                'file_type' => 'pdf',
                'is_active' => true,
                'published_date' => now(),
                'version' => '2024.1',
            ],
            [
                'name' => 'Lower Limb Systems',
                'slug' => 'lower-limb-systems',
                'description' => 'Complete guide to lower limb prosthetics including microprocessor knees and advanced ankle systems.',
                'category' => 'Lower Limb',
                'file_path' => 'catalogs/lower-limb-catalog.pdf',
                'file_size' => '10.1 MB',
                'file_type' => 'pdf',
                'is_active' => true,
                'published_date' => now(),
                'version' => '2024.1',
            ],
            [
                'name' => 'Pediatric Prosthetics Guide',
                'slug' => 'pediatric-prosthetics-guide',
                'description' => 'Specialized catalog for pediatric prosthetic solutions with growth-accommodating features.',
                'category' => 'Pediatric',
                'file_path' => 'catalogs/pediatric-catalog.pdf',
                'file_size' => '6.8 MB',
                'file_type' => 'pdf',
                'is_active' => true,
                'published_date' => now(),
                'version' => '2024.1',
            ],
            [
                'name' => 'Sports & Recreation Prosthetics',
                'slug' => 'sports-recreation-prosthetics',
                'description' => 'High-performance prosthetic solutions designed for sports and recreational activities.',
                'category' => 'Sports',
                'file_path' => 'catalogs/sports-catalog.pdf',
                'file_size' => '5.4 MB',
                'file_type' => 'pdf',
                'is_active' => true,
                'published_date' => now(),
                'version' => '2024.1',
            ],
            [
                'name' => 'Technical Specifications Manual',
                'slug' => 'technical-specifications-manual',
                'description' => 'Detailed technical documentation including measurements, materials, and engineering specifications.',
                'category' => 'Technical',
                'file_path' => 'catalogs/technical-manual.pdf',
                'file_size' => '15.7 MB',
                'file_type' => 'pdf',
                'is_active' => true,
                'published_date' => now(),
                'version' => '2024.1',
            ],
        ];

        foreach ($catalogs as $catalog) {
            Catalog::create($catalog);
        }
    }
}
