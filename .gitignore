# Laravel Backend .gitignore for Shared Hosting Deployment
# This configuration INCLUDES vendor directory for shared hosting compatibility

# Environment files (ALWAYS EXCLUDE)
.env
.env.backup
.env.production
.env.local
.env.staging
.env.testing

# Laravel specific cache and logs
/storage/*.key
/storage/app/*
!/storage/app/.gitkeep
!/storage/app/public
/storage/framework/cache/*
!/storage/framework/cache/.gitkeep
/storage/framework/sessions/*
!/storage/framework/sessions/.gitkeep
/storage/framework/testing/*
!/storage/framework/testing/.gitkeep
/storage/framework/views/*
!/storage/framework/views/.gitkeep
/storage/logs/*
!/storage/logs/.gitkeep

# Bootstrap cache
/bootstrap/cache/*
!/bootstrap/cache/.gitkeep

# Laravel Mix / Vite
/public/hot
/public/storage
/public/mix-manifest.json
/public/build

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Laravel specific
/config/database.php.backup
/database/*.sqlite
/database/*.sqlite-journal

# Composer dependencies (STANDARD PRACTICE - install on server with 'composer install')
# Vendor dependencies should be installed on the deployment server using:
# composer install --optimize-autoloader --no-dev
# This reduces repository size and follows Laravel best practices
/vendor/

# NPM dependencies (install on server with 'npm install' if needed)
/node_modules/

# Package managers
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
.phpunit.result.cache
/coverage/
/.phpunit.cache

# Deployment
/deploy.php
/Envoy.blade.php

# Laravel Telescope
/storage/telescope/*

# Laravel Horizon
/storage/horizon/*

# Laravel Nova
/nova/

# Backup files
*.bak
*.backup

# Local development
/public_html/
/httpdocs/

# Shared hosting specific
.htaccess.backup
error_log
php_errors.log

# Laravel Sanctum
/storage/oauth-private.key
/storage/oauth-public.key

# Laravel Passport
/storage/oauth-*.key

# Custom application logs
/storage/app/logs/*
!/storage/app/logs/.gitkeep

# Filament specific
/storage/app/filament/*
!/storage/app/filament/.gitkeep

# Media library
/storage/app/public/media/*
!/storage/app/public/media/.gitkeep

# Spatie backup
/storage/app/backups/*
!/storage/app/backups/.gitkeep

# Local configuration overrides
/config/local.php
/config/production.php
/config/staging.php

# Development tools
/.vagrant
/Homestead.json
/Homestead.yaml
/.env.dusk.local
/tests/Browser/screenshots/*
/tests/Browser/console/*

# Performance monitoring
/storage/debugbar/*
!/storage/debugbar/.gitkeep

# Custom uploads (preserve structure but ignore content)
/public/uploads/*
!/public/uploads/.gitkeep
/public/images/uploads/*
!/public/images/uploads/.gitkeep

# Shared hosting error logs
error_log.*
php_error.log
access.log

# Temporary development files
*.dev
*.development
*.staging
*.production

# Laravel Octane
/storage/octane/*
!/storage/octane/.gitkeep

# Laravel Sail
/docker-compose.override.yml

# Local SSL certificates
*.pem
*.crt
*.key

# Database dumps
*.sql
*.dump

# Shared hosting specific files
.cpanel.yml
.well-known/

# Custom batch files (development only)
*.bat
*.sh
!artisan

# Documentation (if you want to exclude)
# README.md
# *.md

# Keep important files that might be accidentally ignored
!.htaccess
!public/.htaccess
!storage/app/public/.gitkeep
!public/index.php
!artisan
