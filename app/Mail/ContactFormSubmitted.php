<?php

namespace App\Mail;

use App\Models\ContactSubmission;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContactFormSubmitted extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public ContactSubmission $contactSubmission;

    /**
     * Create a new message instance.
     */
    public function __construct(ContactSubmission $contactSubmission)
    {
        $this->contactSubmission = $contactSubmission;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Contact Form Submission - ' . $this->contactSubmission->subject,
            replyTo: [
                $this->contactSubmission->email => $this->contactSubmission->name,
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.contact-form-submitted',
            with: [
                'submission' => $this->contactSubmission,
                'submissionDate' => $this->contactSubmission->created_at->format('F j, Y \a\t g:i A'),
                'ipAddress' => $this->contactSubmission->ip_address,
                'userAgent' => $this->contactSubmission->user_agent,
                'companyName' => 'RELIFE INC',
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
