<?php

namespace App\Mail;

use App\Models\ContactSubmission;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContactFormThankYou extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public ContactSubmission $contactSubmission;

    /**
     * Create a new message instance.
     */
    public function __construct(ContactSubmission $contactSubmission)
    {
        $this->contactSubmission = $contactSubmission;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Thank you for contacting RELIFE INC',
            from: [
                config('mail.from.address', '<EMAIL>') => config('mail.from.name', 'RELIFE INC'),
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.contact-form-thank-you',
            with: [
                'submission' => $this->contactSubmission,
                'customerName' => $this->contactSubmission->name,
                'submissionDate' => $this->contactSubmission->created_at->format('F j, Y \a\t g:i A'),
                'expectedResponseTime' => '24 hours',
                'companyName' => 'RELIFE INC',
                'companyWebsite' => config('app.url', 'https://re-life.ca'),
                'companyEmail' => config('mail.business_email', '<EMAIL>'),
                'companyPhone' => '+****************',
                'companyAddress' => 'EDGEROOK DR, TORONTO ON M9V 5E8 CANADA',
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
