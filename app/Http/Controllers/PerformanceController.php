<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class PerformanceController extends Controller
{
    /**
     * Get performance metrics
     */
    public function metrics(): JsonResponse
    {
        $startTime = microtime(true);
        
        // Test database connection
        $dbStart = microtime(true);
        try {
            DB::connection()->getPdo();
            $dbConnected = true;
            $dbTime = (microtime(true) - $dbStart) * 1000;
        } catch (\Exception $e) {
            $dbConnected = false;
            $dbTime = null;
        }
        
        // Test cache
        $cacheStart = microtime(true);
        Cache::put('performance_test', 'test_value', 60);
        $cacheValue = Cache::get('performance_test');
        $cacheWorking = $cacheValue === 'test_value';
        $cacheTime = (microtime(true) - $cacheStart) * 1000;
        
        // Memory usage
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        
        // PHP configuration
        $phpConfig = [
            'version' => PHP_VERSION,
            'opcache_enabled' => extension_loaded('Zend OPcache') && ini_get('opcache.enable'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
        ];
        
        $totalTime = (microtime(true) - $startTime) * 1000;
        
        return response()->json([
            'status' => 'success',
            'timestamp' => now()->toISOString(),
            'response_time_ms' => round($totalTime, 2),
            'database' => [
                'connected' => $dbConnected,
                'connection_time_ms' => $dbTime ? round($dbTime, 2) : null,
                'driver' => config('database.default'),
            ],
            'cache' => [
                'working' => $cacheWorking,
                'response_time_ms' => round($cacheTime, 2),
                'driver' => config('cache.default'),
            ],
            'memory' => [
                'current_mb' => round($memoryUsage / 1024 / 1024, 2),
                'peak_mb' => round($memoryPeak / 1024 / 1024, 2),
            ],
            'php' => $phpConfig,
            'laravel' => [
                'version' => app()->version(),
                'environment' => app()->environment(),
                'debug' => config('app.debug'),
            ],
            'optimizations' => [
                'config_cached' => file_exists(base_path('bootstrap/cache/config.php')),
                'routes_cached' => file_exists(base_path('bootstrap/cache/routes-v7.php')),
                'views_cached' => count(glob(storage_path('framework/views/*.php'))) > 0,
                'filament_cached' => is_dir(base_path('bootstrap/cache/filament')),
            ]
        ]);
    }
}
