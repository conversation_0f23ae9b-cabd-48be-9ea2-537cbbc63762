<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProductApiResource;
use App\Models\Product;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $products = QueryBuilder::for(Product::class)
            ->allowedFilters(['name', 'category.name', 'is_featured', 'status'])
            ->allowedSorts(['name', 'price', 'created_at', 'sort_order'])
            ->allowedIncludes(['category', 'media'])
            ->with(['category', 'media'])
            ->active()
            ->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => ProductApiResource::collection($products->items()),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ],
        ]);
    }

    public function show($slug)
    {
        $product = Product::with(['category', 'media'])
            ->where('slug', $slug)
            ->active()
            ->firstOrFail();

        return response()->json([
            'success' => true,
            'data' => new ProductApiResource($product),
        ]);
    }

    public function featured()
    {
        $products = Product::with(['category', 'media'])
            ->active()
            ->featured()
            ->orderBy('sort_order')
            ->limit(6)
            ->get();

        return response()->json([
            'success' => true,
            'data' => ProductApiResource::collection($products),
        ]);
    }

    public function search(Request $request)
    {
        $query = $request->get('q');
        
        if (!$query) {
            return response()->json([
                'success' => false,
                'message' => 'Search query is required',
            ], 400);
        }

        $products = Product::with(['category', 'media'])
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('short_description', 'like', "%{$query}%")
                  ->orWhereHas('category', function ($categoryQuery) use ($query) {
                      $categoryQuery->where('name', 'like', "%{$query}%");
                  });
            })
            ->active()
            ->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => ProductApiResource::collection($products->items()),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ],
        ]);
    }
}
