<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\CategoryApiResource;
use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index()
    {
        $categories = Category::active()
            ->orderBy('sort_order')
            ->get();

        return response()->json([
            'success' => true,
            'data' => CategoryApiResource::collection($categories),
        ]);
    }

    public function show($slug)
    {
        $category = Category::with(['products.media'])
            ->where('slug', $slug)
            ->active()
            ->firstOrFail();

        return response()->json([
            'success' => true,
            'data' => new CategoryApiResource($category),
        ]);
    }

    public function products($slug, Request $request)
    {
        $category = Category::where('slug', $slug)->active()->firstOrFail();
        
        $products = $category->products()
            ->with(['media'])
            ->active()
            ->orderBy('sort_order')
            ->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => $products,
            'category' => $category,
        ]);
    }
}
