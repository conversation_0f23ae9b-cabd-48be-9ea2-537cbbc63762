<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\CatalogApiResource;
use App\Models\Catalog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CatalogController extends Controller
{
    public function index(Request $request)
    {
        $query = Catalog::active()->orderBy('published_date', 'desc');

        // Filter by category if provided
        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $catalogs = $query->get();

        return response()->json([
            'success' => true,
            'data' => CatalogApiResource::collection($catalogs),
        ]);
    }

    public function show($slug)
    {
        $catalog = Catalog::active()->where('slug', $slug)->firstOrFail();

        return response()->json([
            'success' => true,
            'data' => new CatalogApiResource($catalog),
        ]);
    }

    public function download($slug)
    {
        $catalog = Catalog::active()->where('slug', $slug)->firstOrFail();

        if (!$catalog->file_path || !Storage::disk('public')->exists($catalog->file_path)) {
            return response()->json([
                'success' => false,
                'message' => 'Catalog file not found.',
            ], 404);
        }

        // Increment download count
        $catalog->incrementDownloadCount();

        // Get the file path
        $filePath = Storage::disk('public')->path($catalog->file_path);
        $fileName = $catalog->name . '.pdf';

        return response()->download($filePath, $fileName, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        ]);
    }

    public function categories()
    {
        $categories = Catalog::select('category')
            ->active()
            ->distinct()
            ->pluck('category');

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }
}
