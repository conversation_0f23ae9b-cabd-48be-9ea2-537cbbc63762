<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use App\Services\RecaptchaService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:5000',
            'captcha_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Verify CAPTCHA
        $recaptchaService = new RecaptchaService();
        if ($recaptchaService->isEnabled()) {
            $isValidCaptcha = $recaptchaService->verify(
                $request->captcha_token,
                $request->ip()
            );

            if (!$isValidCaptcha) {
                return response()->json([
                    'success' => false,
                    'message' => 'CAPTCHA verification failed. Please try again.',
                    'errors' => ['captcha_token' => ['Invalid CAPTCHA verification']],
                ], 422);
            }
        }

        $contactSubmission = ContactSubmission::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'message' => $request->message,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Email notifications are now handled automatically by ContactSubmissionObserver

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your message. We have received your inquiry and will get back to you within 24 hours.',
            'data' => [
                'id' => $contactSubmission->id,
                'name' => $contactSubmission->name,
                'email' => $contactSubmission->email,
                'subject' => $contactSubmission->subject,
                'created_at' => $contactSubmission->created_at,
            ],
        ], 201);
    }
}
