<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ConfigController extends Controller
{
    /**
     * Get public configuration values for the frontend
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'recaptcha' => [
                    'site_key' => config('services.recaptcha.site_key'),
                    'enabled' => !empty(config('services.recaptcha.site_key')) && 
                               config('services.recaptcha.site_key') !== 'disabled',
                ],
                'app' => [
                    'name' => config('app.name'),
                    'url' => config('app.url'),
                ],
            ],
        ]);
    }

    /**
     * Get reCAPTCHA configuration specifically
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function recaptcha()
    {
        $siteKey = config('services.recaptcha.site_key');
        
        return response()->json([
            'success' => true,
            'data' => [
                'site_key' => $siteKey,
                'enabled' => !empty($siteKey) && $siteKey !== 'disabled',
            ],
        ]);
    }
}
