<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class SpaController extends Controller
{
    /**
     * Serve the React SPA for all frontend routes
     * This handles client-side routing by always returning the main index.html
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $indexPath = public_path('index.html');
        
        // Check if the React build exists
        if (!File::exists($indexPath)) {
            abort(404, 'React application not found. Please run "npm run build" first.');
        }
        
        // Return the React index.html for all frontend routes
        return response(File::get($indexPath))
            ->header('Content-Type', 'text/html')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }
}
