<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductApiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'short_description' => $this->short_description,
            'features' => $this->features,
            'specifications' => $this->specifications,
            'price' => $this->price,
            'sku' => $this->sku,
            'model_number' => $this->model_number,
            'is_active' => $this->is_active,
            'is_featured' => $this->is_featured,
            'status' => $this->status,
            'release_date' => $this->release_date,
            'warranty_info' => $this->warranty_info,
            'compatibility' => $this->compatibility,
            'category' => [
                'id' => $this->category->id,
                'name' => $this->category->name,
                'slug' => $this->category->slug,
            ],
            'images' => $this->getImagesWithPlaceholder(),
            'thumbnails' => $this->getThumbnailsWithPlaceholder(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Get images with placeholder fallback
     */
    private function getImagesWithPlaceholder()
    {
        $media = $this->getMedia('product-images');

        if ($media->count() > 0) {
            return $media->map(function ($media) {
                return [
                    'id' => $media->id,
                    'url' => $media->getUrl(),
                    'thumb' => $media->getUrl('thumb'),
                    'medium' => $media->getUrl('medium'),
                    'large' => $media->getUrl('large'),
                    'alt' => $media->getCustomProperty('alt_text'),
                ];
            });
        }

        // Return placeholder images based on category
        return collect($this->getPlaceholderImages())->map(function ($url, $index) {
            return [
                'id' => $index + 1,
                'url' => $url,
                'thumb' => $url,
                'medium' => $url,
                'large' => $url,
                'alt' => $this->name . ' - Image ' . ($index + 1),
            ];
        });
    }

    /**
     * Get thumbnails with placeholder fallback
     */
    private function getThumbnailsWithPlaceholder()
    {
        $media = $this->getMedia('product-thumbnails');

        if ($media->count() > 0) {
            return $media->map(function ($media) {
                return [
                    'id' => $media->id,
                    'url' => $media->getUrl(),
                    'thumb' => $media->getUrl('thumb'),
                ];
            });
        }

        // Return empty array for thumbnails if no media
        return collect([]);
    }

    /**
     * Get placeholder images based on product category
     */
    private function getPlaceholderImages()
    {
        $categoryName = $this->category->name ?? 'default';
        $baseUrl = config('app.url');

        // Local product images from assets
        $localImages = [
            $baseUrl . '/product1.jpg',
            $baseUrl . '/product2.jpg',
            $baseUrl . '/product3.jpg',
            $baseUrl . '/product4.jpg',
        ];

        // Category-specific placeholder images using local assets
        $placeholders = [
            'Upper Limb Prosthetics' => [
                $localImages[0], // product1.jpg
                $localImages[1], // product2.jpg
                $localImages[2], // product3.jpg
            ],
            'Lower Limb Prosthetics' => [
                $localImages[1], // product2.jpg
                $localImages[3], // product4.jpg
                $localImages[0], // product1.jpg
            ],
            'Pediatric Prosthetics' => [
                $localImages[2], // product3.jpg
                $localImages[0], // product1.jpg
                $localImages[3], // product4.jpg
            ],
            'Sports & Recreation' => [
                $localImages[3], // product4.jpg
                $localImages[1], // product2.jpg
                $localImages[2], // product3.jpg
            ],
            'Accessories & Components' => [
                $localImages[0], // product1.jpg
                $localImages[3], // product4.jpg
                $localImages[1], // product2.jpg
            ],
        ];

        return $placeholders[$categoryName] ?? [
            $localImages[0],
            $localImages[1],
            $localImages[2],
        ];
    }
}
