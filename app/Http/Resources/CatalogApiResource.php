<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CatalogApiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'category' => $this->category,
            'version' => $this->version,
            'file_size' => $this->file_size,
            'file_type' => $this->file_type,
            'download_count' => $this->download_count,
            'published_date' => $this->published_date?->format('Y-m-d'),
            'is_active' => $this->is_active,
            'download_url' => $this->file_path ? route('api.catalogs.download', $this->slug) : null,
            'file_size_formatted' => $this->file_size ? number_format($this->file_size / 1024 / 1024, 2) . ' MB' : null,
            'image_url' => $this->image_path ? asset('storage/' . $this->image_path) : null,
            'thumbnail' => $this->image_path ? asset('storage/' . $this->image_path) : null, // For backward compatibility
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
