<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RecaptchaService
{
    private string $secretKey;
    private string $verifyUrl = 'https://www.google.com/recaptcha/api/siteverify';

    public function __construct()
    {
        $this->secretKey = config('services.recaptcha.secret_key');
    }

    /**
     * Verify the reCAPTCHA token
     *
     * @param string $token
     * @param string|null $remoteIp
     * @return bool
     */
    public function verify(string $token, ?string $remoteIp = null): bool
    {
        if (empty($token)) {
            return false;
        }

        try {
            $response = Http::asForm()->post($this->verifyUrl, [
                'secret' => $this->secretKey,
                'response' => $token,
                'remoteip' => $remoteIp,
            ]);

            if (!$response->successful()) {
                Log::error('reCAPTCHA API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
                return false;
            }

            $data = $response->json();

            if (!isset($data['success'])) {
                Log::error('Invalid reCAPTCHA response format', ['response' => $data]);
                return false;
            }

            if (!$data['success']) {
                Log::warning('reCAPTCHA verification failed', [
                    'error-codes' => $data['error-codes'] ?? [],
                    'token' => substr($token, 0, 20) . '...',
                ]);
                return false;
            }

            // Optional: Check score for v3 (not needed for v2)
            if (isset($data['score'])) {
                $minScore = config('services.recaptcha.min_score', 0.5);
                if ($data['score'] < $minScore) {
                    Log::warning('reCAPTCHA score too low', [
                        'score' => $data['score'],
                        'min_score' => $minScore,
                    ]);
                    return false;
                }
            }

            return true;

        } catch (\Exception $e) {
            Log::error('reCAPTCHA verification exception', [
                'message' => $e->getMessage(),
                'token' => substr($token, 0, 20) . '...',
            ]);
            return false;
        }
    }

    /**
     * Check if reCAPTCHA is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return !empty($this->secretKey) && $this->secretKey !== 'disabled';
    }
}
