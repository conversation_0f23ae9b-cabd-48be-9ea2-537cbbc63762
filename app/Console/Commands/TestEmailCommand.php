<?php

namespace App\Console\Commands;

use App\Mail\ContactFormSubmitted;
use App\Mail\ContactFormThankYou;
use App\Models\ContactSubmission;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {--owner-email=<EMAIL>} {--customer-email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the dual email notification system for contact form submissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing RELIFE INC Email System');
        $this->info('================================================');

        // Create a test contact submission
        $testSubmission = new ContactSubmission([
            'name' => 'Test User for SMTP',
            'email' => $this->option('customer-email'),
            'phone' => '******-123-4567',
            'subject' => 'product-inquiry',
            'message' => 'This is a test message to verify the SMTP email configuration and dual email notification system for RELIFE INC. Please confirm both owner notification and customer thank-you emails are working properly.',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Command',
            'status' => 'new',
        ]);

        // Set timestamps manually since we're not saving to database
        $testSubmission->created_at = now();
        $testSubmission->updated_at = now();
        $testSubmission->id = 999; // Test ID

        $ownerEmail = $this->option('owner-email');
        $customerEmail = $this->option('customer-email');

        $this->info("📧 Owner Email: {$ownerEmail}");
        $this->info("👤 Customer Email: {$customerEmail}");
        $this->newLine();

        // Test 1: Owner Notification Email
        $this->info('📤 Sending Owner Notification Email...');
        try {
            Mail::to($ownerEmail)->send(new ContactFormSubmitted($testSubmission));
            $this->info('✅ Owner notification email sent successfully!');
        } catch (\Exception $e) {
            $this->error('❌ Failed to send owner notification email:');
            $this->error($e->getMessage());
            return 1;
        }

        $this->newLine();

        // Test 2: Customer Thank-You Email
        $this->info('📤 Sending Customer Thank-You Email...');
        try {
            Mail::to($customerEmail, 'Test User for SMTP')->send(new ContactFormThankYou($testSubmission));
            $this->info('✅ Customer thank-you email sent successfully!');
        } catch (\Exception $e) {
            $this->error('❌ Failed to send customer thank-you email:');
            $this->error($e->getMessage());
            return 1;
        }

        $this->newLine();
        $this->info('🎉 Email test completed successfully!');
        $this->info('📋 Please check the following:');
        $this->info("   • Owner notification email delivered to: {$ownerEmail}");
        $this->info("   • Customer thank-you email delivered to: {$customerEmail}");
        $this->info('   • Both emails use RELIFE INC branding');
        $this->info('   • Email templates display correctly');

        return 0;
    }
}
