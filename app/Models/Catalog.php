<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
class Catalog extends Model
{
    use HasFactory, HasSlug, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'category',
        'file_path',
        'file_size',
        'file_type',
        'thumbnail',
        'image_path',
        'is_active',
        'download_count',
        'published_date',
        'version',
        'sort_order',
        'catalog_pdf', // For file upload handling
        'catalog_image', // For image upload handling
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'published_date' => 'date',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }





    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function incrementDownloadCount()
    {
        $this->increment('download_count');
    }
}
