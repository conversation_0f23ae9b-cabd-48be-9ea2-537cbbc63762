<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;

class PerformanceServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register performance optimizations
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Disable lazy loading in production to prevent N+1 queries
        if (app()->environment('production')) {
            Model::preventLazyLoading();
        }

        // Enable query logging only in local environment
        if (app()->environment('local')) {
            DB::listen(function ($query) {
                if ($query->time > 1000) { // Log slow queries (>1 second)
                    logger()->warning('Slow query detected', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'time' => $query->time . 'ms'
                    ]);
                }
            });
        }

        // Optimize view compilation
        View::addExtension('blade.php', 'blade');
        
        // Set default string length for MySQL
        \Illuminate\Support\Facades\Schema::defaultStringLength(191);
    }
}
