<?php

namespace App\Filament\Widgets;

use App\Models\Product;
use App\Models\Category;
use Filament\Widgets\ChartWidget;

class ProductCategoryChart extends ChartWidget
{
    protected static ?string $heading = 'Products by Category';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $categories = Category::withCount('products')->get();

        return [
            'datasets' => [
                [
                    'label' => 'Products per Category',
                    'data' => $categories->pluck('products_count')->toArray(),
                    'backgroundColor' => [
                        '#0369a1', // Primary blue
                        '#16a34a', // Success green
                        '#dc2626', // Danger red
                        '#ca8a04', // Warning yellow
                        '#9333ea', // Purple
                        '#c2410c', // Orange
                    ],
                    'borderColor' => [
                        '#0369a1',
                        '#16a34a',
                        '#dc2626',
                        '#ca8a04',
                        '#9333ea',
                        '#c2410c',
                    ],
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $categories->pluck('name')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
