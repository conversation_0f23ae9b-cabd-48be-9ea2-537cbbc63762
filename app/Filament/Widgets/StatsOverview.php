<?php

namespace App\Filament\Widgets;

use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Products', Product::count())
                ->description('All products in the system')
                ->descriptionIcon('heroicon-m-cube')
                ->color('success'),
            
            Stat::make('Active Products', Product::where('is_active', true)->count())
                ->description('Currently active products')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('primary'),
            
            Stat::make('Featured Products', Product::where('is_featured', true)->count())
                ->description('Featured products')
                ->descriptionIcon('heroicon-m-star')
                ->color('warning'),
            
            Stat::make('Categories', Category::count())
                ->description('Product categories')
                ->descriptionIcon('heroicon-m-tag')
                ->color('info'),
            
            Stat::make('Total Users', User::count())
                ->description('Registered users')
                ->descriptionIcon('heroicon-m-users')
                ->color('gray'),
            
            Stat::make('Average Price', '$' . number_format(Product::avg('price'), 2))
                ->description('Average product price')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),
        ];
    }
}
