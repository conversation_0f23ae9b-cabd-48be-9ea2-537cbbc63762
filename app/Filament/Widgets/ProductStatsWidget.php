<?php

namespace App\Filament\Widgets;

use App\Models\Product;
use App\Models\Category;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ProductStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        return [
            Stat::make('Total Products', Product::count())
                ->description('All products in the system')
                ->descriptionIcon('heroicon-m-cube')
                ->color('primary')
                ->chart([7, 2, 10, 3, 15, 4, 17]),

            Stat::make('Active Products', Product::where('is_active', true)->count())
                ->description('Currently active products')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success')
                ->chart([3, 5, 8, 12, 15, 18, 20]),

            Stat::make('Featured Products', Product::where('is_featured', true)->count())
                ->description('Products marked as featured')
                ->descriptionIcon('heroicon-m-star')
                ->color('warning')
                ->chart([1, 2, 3, 4, 5, 6, 7]),

            Stat::make('Categories', Category::where('is_active', true)->count())
                ->description('Active product categories')
                ->descriptionIcon('heroicon-m-tag')
                ->color('info')
                ->chart([2, 3, 4, 5, 6, 7, 8]),

            Stat::make('Avg. Price', '$' . number_format(Product::whereNotNull('price')->avg('price') ?? 0, 2))
                ->description('Average product price')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success')
                ->chart([100, 120, 110, 130, 125, 140, 135]),

            Stat::make('Products This Month', Product::whereMonth('created_at', now()->month)->count())
                ->description('New products added this month')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('primary')
                ->chart([0, 1, 2, 3, 2, 4, 5]),
        ];
    }

    protected function getColumns(): int
    {
        return 3;
    }
}
