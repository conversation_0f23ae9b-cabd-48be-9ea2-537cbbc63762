<?php

namespace App\Filament\Notifications;

use Filament\Notifications\Notification;

class AdminActionNotification
{
    public static function success(string $title, string $body = null): Notification
    {
        return Notification::make()
            ->title($title)
            ->body($body)
            ->success()
            ->duration(5000)
            ->icon('heroicon-o-check-circle');
    }

    public static function warning(string $title, string $body = null): Notification
    {
        return Notification::make()
            ->title($title)
            ->body($body)
            ->warning()
            ->duration(7000)
            ->icon('heroicon-o-exclamation-triangle');
    }

    public static function error(string $title, string $body = null): Notification
    {
        return Notification::make()
            ->title($title)
            ->body($body)
            ->danger()
            ->duration(10000)
            ->icon('heroicon-o-x-circle');
    }

    public static function info(string $title, string $body = null): Notification
    {
        return Notification::make()
            ->title($title)
            ->body($body)
            ->info()
            ->duration(5000)
            ->icon('heroicon-o-information-circle');
    }

    public static function bulkAction(string $action, int $count, string $resource): Notification
    {
        return self::success(
            "Bulk Action Completed",
            "{$action} applied to {$count} {$resource} record(s) successfully."
        );
    }

    public static function recordCreated(string $resource, string $name = null): Notification
    {
        $title = $name ? "{$resource} '{$name}' Created" : "{$resource} Created";
        return self::success($title, "The {$resource} has been created successfully.");
    }

    public static function recordUpdated(string $resource, string $name = null): Notification
    {
        $title = $name ? "{$resource} '{$name}' Updated" : "{$resource} Updated";
        return self::success($title, "The {$resource} has been updated successfully.");
    }

    public static function recordDeleted(string $resource, string $name = null): Notification
    {
        $title = $name ? "{$resource} '{$name}' Deleted" : "{$resource} Deleted";
        return self::warning($title, "The {$resource} has been moved to trash.");
    }

    public static function recordRestored(string $resource, string $name = null): Notification
    {
        $title = $name ? "{$resource} '{$name}' Restored" : "{$resource} Restored";
        return self::success($title, "The {$resource} has been restored successfully.");
    }
}
