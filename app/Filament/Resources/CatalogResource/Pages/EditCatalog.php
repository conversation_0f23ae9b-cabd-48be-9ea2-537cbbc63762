<?php

namespace App\Filament\Resources\CatalogResource\Pages;

use App\Filament\Resources\CatalogResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Storage;

class EditCatalog extends EditRecord
{
    protected static string $resource = CatalogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Don't pre-populate file upload fields to avoid issues
        // The file upload components will handle existing files automatically
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Handle PDF file upload
        if (isset($data['catalog_pdf']) && $data['catalog_pdf'] && !is_string($data['catalog_pdf'])) {
            try {
                // Delete old file if exists
                if ($this->record->file_path && Storage::disk('public')->exists($this->record->file_path)) {
                    Storage::disk('public')->delete($this->record->file_path);
                }

                // Store the new file
                $path = Storage::disk('public')->putFile('catalogs', $data['catalog_pdf']);

                // Set the file information
                $data['file_path'] = $path;
                $data['file_size'] = $data['catalog_pdf']->getSize();
                $data['file_type'] = 'pdf';
            } catch (\Exception $e) {
                // Log the error but don't fail the update
                \Log::error('File upload error during edit: ' . $e->getMessage());
            }
        }

        // Handle image upload
        if (isset($data['catalog_image']) && $data['catalog_image'] && !is_string($data['catalog_image'])) {
            try {
                // Delete old image if exists
                if ($this->record->image_path && Storage::disk('public')->exists($this->record->image_path)) {
                    Storage::disk('public')->delete($this->record->image_path);
                }

                // Store the new image
                $imagePath = Storage::disk('public')->putFile('catalog-images', $data['catalog_image']);
                $data['image_path'] = $imagePath;
            } catch (\Exception $e) {
                // Log the error but don't fail the update
                \Log::error('Image upload error during edit: ' . $e->getMessage());
            }
        }

        // Remove the temporary fields
        unset($data['catalog_pdf']);
        unset($data['catalog_image']);

        return $data;
    }
}
