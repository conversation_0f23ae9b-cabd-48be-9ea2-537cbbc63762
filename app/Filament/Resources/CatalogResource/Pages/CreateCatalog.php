<?php

namespace App\Filament\Resources\CatalogResource\Pages;

use App\Filament\Resources\CatalogResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CreateCatalog extends CreateRecord
{
    protected static string $resource = CatalogResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Auto-generate slug if not provided
        if (empty($data['slug']) && !empty($data['name'])) {
            $data['slug'] = \Str::slug($data['name']);
        }

        // Handle PDF file upload
        if (isset($data['catalog_pdf']) && $data['catalog_pdf']) {
            $uploadedFile = $data['catalog_pdf'];

            // Check if it's a string (existing file) or uploaded file
            if (is_string($uploadedFile)) {
                // It's already a file path
                $data['file_path'] = $uploadedFile;
                $data['file_type'] = 'pdf';

                // Try to get file size
                $fullPath = storage_path('app/public/' . $uploadedFile);
                if (file_exists($fullPath)) {
                    $data['file_size'] = filesize($fullPath);
                }
            } else {
                // It's an uploaded file object
                try {
                    $path = Storage::disk('public')->putFile('catalogs', $uploadedFile);
                    $data['file_path'] = $path;
                    $data['file_size'] = $uploadedFile->getSize();
                    $data['file_type'] = 'pdf';
                } catch (\Exception $e) {
                    // Log the error but don't fail the creation
                    \Log::error('File upload error: ' . $e->getMessage());
                }
            }

            // Remove the temporary field
            unset($data['catalog_pdf']);
        } else {
            // No file uploaded, set defaults
            $data['file_type'] = 'pdf';
            $data['file_size'] = null;
            $data['file_path'] = null;
        }

        // Handle image upload
        if (isset($data['catalog_image']) && $data['catalog_image']) {
            $uploadedImage = $data['catalog_image'];

            // Check if it's a string (existing file) or uploaded file
            if (is_string($uploadedImage)) {
                // It's already a file path
                $data['image_path'] = $uploadedImage;
            } else {
                // It's an uploaded file object
                try {
                    $imagePath = Storage::disk('public')->putFile('catalog-images', $uploadedImage);
                    $data['image_path'] = $imagePath;
                } catch (\Exception $e) {
                    // Log the error but don't fail the creation
                    \Log::error('Image upload error: ' . $e->getMessage());
                }
            }

            // Remove the temporary field
            unset($data['catalog_image']);
        }

        return $data;
    }
}
