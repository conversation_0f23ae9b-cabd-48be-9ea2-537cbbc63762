<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CatalogResource\Pages;
use App\Filament\Resources\CatalogResource\RelationManagers;
use App\Models\Catalog;
use App\Filament\Notifications\AdminActionNotification;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class CatalogResource extends Resource
{
    protected static ?string $model = Catalog::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'Catalogs';

    protected static ?string $modelLabel = 'Catalog';

    protected static ?string $pluralModelLabel = 'Catalogs';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->minLength(3)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $context, $state, callable $set, callable $get) {
                                if ($context === 'create' && empty($get('slug'))) {
                                    $set('slug', Str::slug($state));
                                }
                            })
                            ->rules(['regex:/^[a-zA-Z0-9\s\-_&()\.]+$/'])
                            ->validationMessages([
                                'regex' => 'Catalog name can only contain letters, numbers, spaces, hyphens, underscores, ampersands, periods, and parentheses.',
                            ])
                            ->helperText('Enter a descriptive catalog name (3-255 characters)'),

                        Forms\Components\TextInput::make('slug')
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('Leave empty to auto-generate from name.')
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $context, $state, callable $set, callable $get) {
                                if ($context === 'create' && empty($state)) {
                                    $name = $get('name');
                                    if ($name) {
                                        $set('slug', \Str::slug($name));
                                    }
                                }
                            }),

                        Forms\Components\Textarea::make('description')
                            ->required()
                            ->maxLength(1000)
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('category')
                            ->required()
                            ->options([
                                'General' => 'General Catalog',
                                'Upper Limb Prosthetics' => 'Upper Limb Prosthetics',
                                'Lower Limb Prosthetics' => 'Lower Limb Prosthetics',
                                'Pediatric Prosthetics' => 'Pediatric Prosthetics',
                                'Sports & Recreation' => 'Sports & Recreation',
                                'Accessories & Components' => 'Accessories & Components',
                            ])
                            ->searchable(),

                        Forms\Components\TextInput::make('version')
                            ->maxLength(50)
                            ->placeholder('e.g., v1.0, 2024.1')
                            ->helperText('Optional version identifier'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Files')
                    ->schema([
                        Forms\Components\FileUpload::make('catalog_pdf')
                            ->label('PDF Catalog (Optional)')
                            ->acceptedFileTypes(['application/pdf'])
                            ->maxSize(50 * 1024) // 50MB
                            ->directory('catalogs')
                            ->preserveFilenames()
                            ->downloadable()
                            ->openable()
                            ->columnSpanFull()
                            ->rules(['mimes:pdf', 'max:51200']) // 50MB in KB
                            ->validationMessages([
                                'mimes' => 'Only PDF files are allowed.',
                                'max' => 'PDF file size cannot exceed 50MB.',
                            ])
                            ->helperText('Upload PDF catalog file. Maximum size: 50MB. Only PDF format accepted.'),

                        Forms\Components\FileUpload::make('catalog_image')
                            ->label('Catalog Thumbnail Image (Optional)')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'image/jpg'])
                            ->maxSize(5 * 1024) // 5MB
                            ->directory('catalog-images')
                            ->image()
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('16:9')
                            ->imageResizeTargetWidth('800')
                            ->imageResizeTargetHeight('450')
                            ->columnSpanFull()
                            ->rules(['image', 'max:5120']) // 5MB in KB
                            ->validationMessages([
                                'image' => 'File must be a valid image.',
                                'max' => 'Image size cannot exceed 5MB.',
                            ])
                            ->helperText('Upload thumbnail image. Max 5MB. Recommended size: 800x450px. Supports JPG, PNG, WebP.'),
                    ]),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\DatePicker::make('published_date')
                            ->default(now()),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->helperText('Only active catalogs will be available for download'),

                        Forms\Components\TextInput::make('download_count')
                            ->label('Download Count')
                            ->numeric()
                            ->default(0)
                            ->disabled()
                            ->helperText('Automatically tracked'),

                        Forms\Components\TextInput::make('sort_order')
                            ->numeric()
                            ->default(0),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_path')
                    ->label('Image')
                    ->disk('public')
                    ->height(50)
                    ->width(70)
                    ->defaultImageUrl('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA3MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjcwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNSAyMEgzNVYzMEgyNVYyMFoiIGZpbGw9IiM2QjcyODAiLz4KPHRleHQgeD0iMzUiIHk9IjMwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iOCIgZmlsbD0iIzZCNzI4MCI+UERGPC90ZXh0Pgo8L3N2Zz4K'),

                Tables\Columns\TextColumn::make('name')
                    ->label('Catalog Name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('category')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'General' => 'gray',
                        'Upper Limb Prosthetics' => 'blue',
                        'Lower Limb Prosthetics' => 'green',
                        'Pediatric Prosthetics' => 'yellow',
                        'Sports & Recreation' => 'purple',
                        'Accessories & Components' => 'orange',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('version')
                    ->searchable()
                    ->placeholder('—'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('download_count')
                    ->label('Downloads')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('file_size')
                    ->label('File Size')
                    ->formatStateUsing(fn (?string $state): string =>
                        $state ? number_format($state / 1024 / 1024, 2) . ' MB' : '—'
                    )
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('published_date')
                    ->label('Published')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'General' => 'General Catalog',
                        'Upper Limb Prosthetics' => 'Upper Limb Prosthetics',
                        'Lower Limb Prosthetics' => 'Lower Limb Prosthetics',
                        'Pediatric Prosthetics' => 'Pediatric Prosthetics',
                        'Sports & Recreation' => 'Sports & Recreation',
                        'Accessories & Components' => 'Accessories & Components',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('All catalogs')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),

                Tables\Filters\Filter::make('has_file')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('file_path'))
                    ->label('Has PDF File'),

                Tables\Filters\Filter::make('download_count')
                    ->form([
                        Forms\Components\TextInput::make('min_downloads')
                            ->label('Minimum Downloads')
                            ->numeric()
                            ->minValue(0),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['min_downloads'],
                            fn (Builder $query, $count): Builder => $query->where('download_count', '>=', $count),
                        );
                    }),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn (Catalog $record): string =>
                        $record->file_path ? asset('storage/' . $record->file_path) : '#'
                    )
                    ->openUrlInNewTab(),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),

                Tables\Actions\Action::make('download')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->url(fn (Catalog $record): string =>
                        $record->file_path ? asset('storage/' . $record->file_path) : '#'
                    )
                    ->openUrlInNewTab()
                    ->action(function (Catalog $record) {
                        $record->incrementDownloadCount();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $count = $records->count();
                            $records->each->update(['is_active' => true]);
                            AdminActionNotification::bulkAction('Activation', $count, 'Catalog')->send();
                        })
                        ->deselectRecordsAfterCompletion()
                        ->requiresConfirmation(),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('warning')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);
                        })
                        ->deselectRecordsAfterCompletion()
                        ->requiresConfirmation(),
                ]),
            ])
            ->reorderable('sort_order')
            ->defaultSort('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCatalogs::route('/'),
            'create' => Pages\CreateCatalog::route('/create'),
            'edit' => Pages\EditCatalog::route('/{record}/edit'),
        ];
    }
}
