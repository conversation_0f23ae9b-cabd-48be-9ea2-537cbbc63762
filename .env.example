APP_NAME="RELIFE Medical Technologies API"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=relife_backend
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

# Queue Configuration for Email Processing
# Use 'database' for development, 'redis' or 'sqs' for production
DB_QUEUE_TABLE=jobs
DB_QUEUE=default
DB_QUEUE_RETRY_AFTER=90

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Contact Form Email Configuration
# Primary email address to receive contact form notifications
MAIL_OWNER_EMAIL="<EMAIL>"
# Additional recipients (comma-separated)
MAIL_CONTACT_FORM_RECIPIENTS="<EMAIL>,<EMAIL>"
# Business email for customer communications
MAIL_BUSINESS_EMAIL="<EMAIL>"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Sanctum Configuration
# For development: localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1
# For production: re-life.ca,www.re-life.ca
SANCTUM_STATEFUL_DOMAINS=localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1,re-life.ca,www.re-life.ca

# Frontend URL
# For development: http://localhost:3000
# For production: https://re-life.ca
FRONTEND_URL=http://localhost:3000

# Session Configuration for SPA
SESSION_DOMAIN=null
SESSION_SECURE_COOKIE=false

# Filament Configuration
FILAMENT_DOMAIN=
FILAMENT_PATH=admin
