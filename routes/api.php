<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PerformanceController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CatalogController;
use App\Http\Controllers\Api\ContactController;
use App\Http\Controllers\Api\ConfigController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Performance monitoring (public endpoint)
Route::get('/performance', [PerformanceController::class, 'metrics']);

// Public API routes
Route::prefix('v1')->group(function () {

    // Products
    Route::get('/products', [ProductController::class, 'index']);
    Route::get('/products/featured', [ProductController::class, 'featured']);
    Route::get('/products/search', [ProductController::class, 'search']);
    Route::get('/products/{slug}', [ProductController::class, 'show']);
    
    // Categories
    Route::get('/categories', [CategoryController::class, 'index']);
    Route::get('/categories/{slug}', [CategoryController::class, 'show']);
    Route::get('/categories/{slug}/products', [CategoryController::class, 'products']);
    
    // Catalogs
    Route::get('/catalogs', [CatalogController::class, 'index']);
    Route::get('/catalogs/categories', [CatalogController::class, 'categories']);
    Route::get('/catalogs/{slug}', [CatalogController::class, 'show']);
    Route::get('/catalogs/{slug}/download', [CatalogController::class, 'download'])->name('api.catalogs.download');
    
    // Contact
    Route::post('/contact', [ContactController::class, 'store']);

    // Configuration
    Route::get('/config', [ConfigController::class, 'index']);
    Route::get('/config/recaptcha', [ConfigController::class, 'recaptcha']);

});

// Protected API routes (require authentication)
Route::middleware(['auth:sanctum'])->prefix('v1')->group(function () {
    // Add protected routes here if needed
});
