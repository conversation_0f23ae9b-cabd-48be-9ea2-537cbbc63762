@import '/vendor/filament/filament/resources/css/theme.css';

@config 'tailwind.config.js';

/* RELIFE Medical Technologies Custom Theme */

/* Primary Brand Colors */
:root {
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #0369a1;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    --primary-950: #172554;
}

/* Enhanced Navigation */
.fi-sidebar-nav {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    border-right: 2px solid var(--primary-500);
}

.fi-sidebar-nav-item-button {
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 8px;
}

.fi-sidebar-nav-item-button:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.fi-sidebar-nav-item-button[aria-current="page"] {
    background: rgba(255, 255, 255, 0.15);
    border-left: 4px solid #ffffff;
}

/* Enhanced Header */
.fi-topbar {
    background: linear-gradient(90deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 2px solid var(--primary-100);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Enhanced Tables */
.fi-ta-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.fi-ta-header {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    font-weight: 600;
}

.fi-ta-row:hover {
    background: var(--primary-25);
    transform: scale(1.001);
    transition: all 0.2s ease;
}

/* Enhanced Forms */
.fi-fo-section {
    border-radius: 12px;
    border: 1px solid var(--primary-100);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.fi-fo-section-header {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    border-bottom: 1px solid var(--primary-200);
}

/* Enhanced Buttons */
.fi-btn-primary {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(3, 105, 161, 0.3);
    transition: all 0.3s ease;
}

.fi-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(3, 105, 161, 0.4);
}

/* Enhanced Cards */
.fi-card {
    border-radius: 12px;
    border: 1px solid var(--primary-100);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.fi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Enhanced Notifications */
.fi-no-notification {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

/* Medical Theme Enhancements */
.medical-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.medical-status-active {
    color: #059669;
    background: #d1fae5;
    border: 1px solid #a7f3d0;
}

.medical-status-inactive {
    color: #dc2626;
    background: #fee2e2;
    border: 1px solid #fecaca;
}

/* Enhanced Drag and Drop */
.sortable-ghost {
    opacity: 0.5;
    background: var(--primary-50);
    border: 2px dashed var(--primary-300);
}

.sortable-chosen {
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(3, 105, 161, 0.3);
}

/* Loading States */
.fi-loading {
    background: linear-gradient(90deg, var(--primary-100) 25%, var(--primary-200) 50%, var(--primary-100) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .fi-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .fi-sidebar.open {
        transform: translateX(0);
    }
}

/* Dark Mode Enhancements */
.dark .fi-sidebar-nav {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.dark .fi-topbar {
    background: linear-gradient(90deg, #1e293b 0%, #334155 100%);
    border-bottom-color: #475569;
}

/* Accessibility Enhancements */
.fi-btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.fi-input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(3, 105, 161, 0.1);
}

/* Print Styles */
@media print {
    .fi-sidebar,
    .fi-topbar,
    .fi-btn {
        display: none !important;
    }
    
    .fi-main {
        margin: 0 !important;
        padding: 0 !important;
    }
}
