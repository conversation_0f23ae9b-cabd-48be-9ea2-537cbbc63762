
import React, { useEffect } from 'react';
import { Target, Lightbulb, Heart, Users, Zap, Award, Shield, Handshake, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, MessageCircle } from 'lucide-react';
import Logo from '../assets/logo.png';

const About = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50/30">
      {/* Enhanced Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-gray-50 py-24 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-2000"></div>
          <div className="absolute bottom-1/3 right-1/4 w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-3000"></div>
          <div className="absolute top-1/2 left-1/2 w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-4000"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-5xl mx-auto text-center">
            <div className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6 animate-fade-in">
              <Heart className="w-4 h-4 inline mr-2" />
              About Our Company
            </div>
            <div className="flex flex-col items-center mb-8 animate-slide-up">
              <h1 className="text-5xl md:text-7xl font-bold mb-6">
                About
              </h1>
              <div className="flex items-center justify-center">
                <img
                  src={Logo}
                  alt="RELIFE Logo"
                  className="h-16 md:h-20 w-auto object-contain"
                />
              </div>
            </div>
            <p className="text-xl md:text-2xl text-gray-700 leading-relaxed max-w-4xl mx-auto animate-slide-up delay-200">
              we've been at the forefront of prosthetic innovation,
              helping individuals regain mobility and confidence through advanced technology
              and compassionate care.
            </p>

            {/* Key Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16 animate-fade-in delay-500">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-blue-100">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Innovation Leader</h3>
                <p className="text-gray-600 text-sm">Pioneering advanced prosthetic technologies</p>
              </div>
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-blue-100">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Patient-Centered</h3>
                <p className="text-gray-600 text-sm">Focused on improving quality of life</p>
              </div>
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-blue-100">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Excellence Driven</h3>
                <p className="text-gray-600 text-sm">Committed to the highest standards</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="animate-fade-in">
              <div className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6">
                <Heart className="w-4 h-4 inline mr-2" />
                Our Purpose
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Our Mission
              </h2>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                We believe that everyone deserves to live life to the fullest, regardless of
                physical challenges. Our mission is to provide cutting-edge prosthetic solutions
                that restore not just mobility, but dignity, independence, and hope.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                Through continuous innovation, personalized care, and unwavering commitment to
                quality, we're helping to redefine what's possible in the field of prosthetics.
              </p>
            </div>

            {/* Enhanced Vision Section */}
            <div className="relative animate-fade-in delay-300">
              <div className="p-8 lg:p-10 text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <Target className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-6">Our Vision</h3>
                <p className="text-lg text-gray-700 leading-relaxed mb-8">
                  To be the global leader in prosthetic innovation, making advanced mobility
                  solutions accessible to everyone who needs them.
                </p>

                {/* Vision Goals */}
                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Lightbulb className="w-4 h-4 text-blue-600" />
                    </div>
                    <span className="text-gray-700 font-medium">Innovation Excellence</span>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Users className="w-4 h-4 text-blue-600" />
                    </div>
                    <span className="text-gray-700 font-medium">Global Accessibility</span>
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-xl">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Heart className="w-4 h-4 text-blue-600" />
                    </div>
                    <span className="text-gray-700 font-medium">Life Transformation</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Values Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-20 animate-fade-in">
            <div className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6">
              <Shield className="w-4 h-4 inline mr-2" />
              Our Foundation
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              These principles guide everything we do, from product development
              to customer service, ensuring we deliver excellence at every step.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="medical-card bg-white/90 backdrop-blur-sm p-8 text-center group animate-fade-in delay-100">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Lightbulb className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Innovation</h3>
              <p className="text-gray-600 leading-relaxed">
                Continuously pushing the boundaries of what's possible in prosthetic technology.
              </p>
            </div>

            <div className="medical-card bg-white/90 backdrop-blur-sm p-8 text-center group animate-fade-in delay-200">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Compassion</h3>
              <p className="text-gray-600 leading-relaxed">
                Understanding the unique challenges and providing personalized care.
              </p>
            </div>

            <div className="medical-card bg-white/90 backdrop-blur-sm p-8 text-center group animate-fade-in delay-300">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Excellence</h3>
              <p className="text-gray-600 leading-relaxed">
                Maintaining the highest standards in product quality and service delivery.
              </p>
            </div>

            <div className="medical-card bg-white/90 backdrop-blur-sm p-8 text-center group animate-fade-in delay-400">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Handshake className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Integrity</h3>
              <p className="text-gray-600 leading-relaxed">
                Building trust through honest communication and ethical business practices.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Team Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-20 animate-fade-in">
            <div className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6">
              <Users className="w-4 h-4 inline mr-2" />
              Our Team
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Expertise
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our multidisciplinary team brings together decades of experience in
              prosthetics, engineering, and patient care to deliver exceptional results.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="medical-card bg-gradient-to-br from-blue-50 to-white p-8 text-center animate-fade-in delay-100">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <UserCheck className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Certified Prosthetists</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Board-certified professionals with extensive experience in prosthetic fitting and care.
              </p>
            </div>

            <div className="medical-card bg-gradient-to-br from-blue-50 to-white p-8 text-center animate-fade-in delay-200">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Wrench className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Engineering Team</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Innovative engineers developing next-generation prosthetic technologies.
              </p>
            </div>

            <div className="medical-card bg-gradient-to-br from-blue-50 to-white p-8 text-center animate-fade-in delay-300">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <MessageCircle className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Support Specialists</h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Dedicated support team providing ongoing care and assistance throughout your journey.
              </p>
            </div>
          </div>
        </div>
      </section>


    </div>
  );
};

export default About;
