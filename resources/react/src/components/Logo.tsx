import React from 'react';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  alt?: string;
}

const Logo: React.FC<LogoProps> = ({ className = '', size = 'md', alt = 'RELIFE Logo' }) => {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-12',
    lg: 'h-16'
  };

  return (
    <div className={`flex items-center ${className}`}>
      <img
        src="/relife-logo.png"
        alt={alt}
        className={`${sizeClasses[size]} w-auto object-contain`}
        style={{ maxWidth: 'none' }}
      />
    </div>
  );
};

export default Logo;
