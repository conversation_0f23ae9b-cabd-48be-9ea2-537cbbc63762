import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';

interface ReCaptchaProps {
  siteKey: string;
  onVerify: (token: string) => void;
  onExpired?: () => void;
  onError?: () => void;
  onLoad?: () => void;
  theme?: 'light' | 'dark';
  size?: 'normal' | 'compact';
  className?: string;
  containerClassName?: string;
}

export interface ReCaptchaRef {
  reset: () => void;
  getResponse: () => string;
}

const ReCaptcha = forwardRef<ReCaptchaRef, ReCaptchaProps>(({
  siteKey,
  onVerify,
  onExpired,
  onError,
  onLoad,
  theme = 'light',
  size = 'normal',
  className = '',
  containerClassName = ''
}, ref) => {
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  useImperativeHandle(ref, () => ({
    reset: () => {
      recaptchaRef.current?.reset();
    },
    getResponse: () => {
      return recaptchaRef.current?.getValue() || '';
    }
  }));

  const handleChange = (token: string | null) => {
    if (token) {
      onVerify(token);
    } else {
      onExpired?.();
    }
  };

  const handleExpired = () => {
    onExpired?.();
  };

  const handleErrored = () => {
    onError?.();
  };

  const handleLoad = () => {
    onLoad?.();
  };

  return (
    <div className={`flex justify-center ${containerClassName}`}>
      <div
        className={`recaptcha-container ${className}`}
        style={{
          transform: 'scale(1)',
          transformOrigin: '0 0',
          borderRadius: '8px',
          overflow: 'hidden',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        }}
      >
        <ReCAPTCHA
          ref={recaptchaRef}
          sitekey={siteKey}
          onChange={handleChange}
          onExpired={handleExpired}
          onErrored={handleErrored}
          onLoad={handleLoad}
          theme={theme}
          size={size}
        />
      </div>
    </div>
  );
});

ReCaptcha.displayName = 'ReCaptcha';

export default ReCaptcha;
