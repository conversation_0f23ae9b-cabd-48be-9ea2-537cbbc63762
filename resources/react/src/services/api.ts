import { Product, Category, ApiResponse } from '../data/products';

// Base API configuration - use relative URL when served from Laravel
const API_BASE_URL = '/api/v1';

// Generic API fetch function
async function apiRequest<T>(endpoint: string, options?: {
  method?: string;
  body?: Record<string, unknown>;
}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  const { method = 'GET', body } = options || {};

  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest', // Important for <PERSON><PERSON> to recognize AJAX requests
      },
      credentials: 'include', // Include cookies for CORS and session handling
      ...(body && { body: JSON.stringify(body) }),
    });

    if (!response.ok) {
      let errorMessage = `API request failed: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();
        console.error(`API Error ${response.status}:`, errorData);

        // Handle <PERSON> validation errors
        if (response.status === 422 && errorData.errors) {
          const validationErrors = Object.values(errorData.errors).flat().join(', ');
          errorMessage = `Validation Error: ${validationErrors}`;
        } else if (errorData.message) {
          errorMessage = errorData.message;
        }
      } catch (parseError) {
        // If response is not JSON, get text
        const errorText = await response.text();
        console.error(`API Error ${response.status}:`, errorText);

        // Handle CSRF token mismatch specifically
        if (response.status === 419 || errorText.includes('CSRF')) {
          errorMessage = 'CSRF token mismatch. Please refresh the page and try again.';
        }
      }

      throw new Error(errorMessage);
    }

    return response.json();
  } catch (error) {
    console.error('API Request Error:', error);

    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Cannot connect to backend server. Please ensure the backend is running.');
    }

    throw error;
  }
}

// Product API functions
export const productApi = {
  // Get all products with optional filters
  getProducts: async (params?: {
    page?: number;
    per_page?: number;
    filter?: { [key: string]: string };
    sort?: string;
  }): Promise<ApiResponse<Product[]>> => {
    const searchParams = new URLSearchParams();
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.per_page) searchParams.append('per_page', params.per_page.toString());
    if (params?.sort) searchParams.append('sort', params.sort);
    
    // Add filters
    if (params?.filter) {
      Object.entries(params.filter).forEach(([key, value]) => {
        searchParams.append(`filter[${key}]`, value);
      });
    }
    
    const queryString = searchParams.toString();
    const endpoint = `/products${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest<ApiResponse<Product[]>>(endpoint);
  },

  // Get featured products
  getFeaturedProducts: async (): Promise<ApiResponse<Product[]>> => {
    return apiRequest<ApiResponse<Product[]>>('/products/featured');
  },

  // Get single product by slug
  getProduct: async (slug: string): Promise<ApiResponse<Product>> => {
    return apiRequest<ApiResponse<Product>>(`/products/${slug}`);
  },

  // Search products
  searchProducts: async (query: string, params?: {
    page?: number;
    per_page?: number;
  }): Promise<ApiResponse<Product[]>> => {
    const searchParams = new URLSearchParams();
    searchParams.append('q', query);
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.per_page) searchParams.append('per_page', params.per_page.toString());
    
    return apiRequest<ApiResponse<Product[]>>(`/products/search?${searchParams.toString()}`);
  },
};

// Category API functions
export const categoryApi = {
  // Get all categories
  getCategories: async (): Promise<ApiResponse<Category[]>> => {
    return apiRequest<ApiResponse<Category[]>>('/categories');
  },

  // Get single category by slug
  getCategory: async (slug: string): Promise<ApiResponse<Category>> => {
    return apiRequest<ApiResponse<Category>>(`/categories/${slug}`);
  },

  // Get products by category
  getCategoryProducts: async (slug: string, params?: {
    page?: number;
    per_page?: number;
  }): Promise<ApiResponse<Product[]>> => {
    const searchParams = new URLSearchParams();
    
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.per_page) searchParams.append('per_page', params.per_page.toString());
    
    return apiRequest<ApiResponse<Product[]>>(`/categories/${slug}/products?${searchParams.toString()}`);
  },
};

// Helper functions to transform API data for legacy components
export const transformProduct = (apiProduct: Product): {
  id: string;
  title: string;
  description: string;
  price: string;
  category: string;
  image: string;
  images: string[];
  specs: { [key: string]: string };
} => {
  // Handle images - use local assets if no images exist
  const hasImages = apiProduct.images && apiProduct.images.length > 0;
  const placeholderImages = [
    '/product1.jpg',
    '/product2.jpg',
    '/product3.jpg',
    '/product4.jpg',
  ];
  const placeholderImage = placeholderImages[0];

  return {
    id: apiProduct.id.toString(),
    title: apiProduct.name,
    description: apiProduct.description,
    price: apiProduct.price ? `$${apiProduct.price}` : 'Contact for Pricing',
    category: apiProduct.category.name,
    image: hasImages ? apiProduct.images[0].url : placeholderImage,
    images: hasImages ? apiProduct.images.map(img => img.url) : placeholderImages,
    specs: apiProduct.specifications || {},
  };
};

export const transformProducts = (apiProducts: Product[]) => {
  return apiProducts.map(transformProduct);
};

// Catalog interfaces
export interface Catalog {
  id: number;
  name: string;
  slug: string;
  description: string;
  category: string;
  version?: string;
  file_size?: number;
  file_type: string;
  download_count: number;
  published_date?: string;
  is_active: boolean;
  download_url?: string;
  file_size_formatted?: string;
  image_url?: string;
  thumbnail?: string; // For backward compatibility
  created_at: string;
  updated_at: string;
}

// Catalog API functions
export const catalogApi = {
  // Get all catalogs with optional filters
  getCatalogs: async (params?: {
    category?: string;
    search?: string;
  }): Promise<ApiResponse<Catalog[]>> => {
    const searchParams = new URLSearchParams();

    if (params?.category) searchParams.append('category', params.category);
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const endpoint = `/catalogs${queryString ? `?${queryString}` : ''}`;

    return apiRequest<ApiResponse<Catalog[]>>(endpoint);
  },

  // Get single catalog by slug
  getCatalog: async (slug: string): Promise<ApiResponse<Catalog>> => {
    return apiRequest<ApiResponse<Catalog>>(`/catalogs/${slug}`);
  },

  // Get catalog categories
  getCategories: async (): Promise<ApiResponse<string[]>> => {
    return apiRequest<ApiResponse<string[]>>('/catalogs/categories');
  },

  // Download catalog (returns download URL)
  getDownloadUrl: (slug: string): string => {
    return `${API_BASE_URL}/catalogs/${slug}/download`;
  },
};

// Contact form types
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  captcha_token?: string;
}

export interface ContactResponse {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
  errors?: { [key: string]: string[] };
}

// Configuration API types
export interface RecaptchaConfig {
  site_key: string;
  enabled: boolean;
}

export interface AppConfig {
  recaptcha: RecaptchaConfig;
  app: {
    name: string;
    url: string;
  };
}

// Configuration API functions
export const configApi = {
  // Get full configuration
  getConfig: async (): Promise<ApiResponse<AppConfig>> => {
    return apiRequest<ApiResponse<AppConfig>>('/config', {
      method: 'GET',
    });
  },

  // Get reCAPTCHA configuration specifically
  getRecaptchaConfig: async (): Promise<ApiResponse<RecaptchaConfig>> => {
    return apiRequest<ApiResponse<RecaptchaConfig>>('/config/recaptcha', {
      method: 'GET',
    });
  },
};

// Contact API functions
export const contactApi = {
  // Submit contact form
  submitContactForm: async (formData: ContactFormData): Promise<ContactResponse> => {
    return apiRequest<ContactResponse>('/contact', {
      method: 'POST',
      body: formData,
    });
  },
};
