import { useQuery } from '@tanstack/react-query';
import { productApi, categoryApi, catalogApi, transformProducts } from '../services/api';
import { Product } from '../data/products';

// Hook for fetching all products
export const useProducts = (params?: {
  page?: number;
  per_page?: number;
  filter?: { [key: string]: string };
  sort?: string;
}) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => productApi.getProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Hook for fetching featured products
export const useFeaturedProducts = () => {
  return useQuery({
    queryKey: ['products', 'featured'],
    queryFn: () => productApi.getFeaturedProducts(),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Hook for fetching a single product by slug
export const useProduct = (slug: string) => {
  return useQuery({
    queryKey: ['products', slug],
    queryFn: () => productApi.getProduct(slug),
    enabled: !!slug,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Hook for searching products
export const useProductSearch = (query: string, params?: {
  page?: number;
  per_page?: number;
}) => {
  return useQuery({
    queryKey: ['products', 'search', query, params],
    queryFn: () => productApi.searchProducts(query, params),
    enabled: !!query && query.length > 2,
    staleTime: 2 * 60 * 1000, // 2 minutes for search results
    gcTime: 5 * 60 * 1000,
  });
};

// Hook for fetching categories
export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: () => categoryApi.getCategories(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Hook for fetching products by category
export const useCategoryProducts = (categorySlug: string, params?: {
  page?: number;
  per_page?: number;
}) => {
  return useQuery({
    queryKey: ['categories', categorySlug, 'products', params],
    queryFn: () => categoryApi.getCategoryProducts(categorySlug, params),
    enabled: !!categorySlug,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Legacy hooks that transform data for existing components
export const useProductsLegacy = (params?: {
  page?: number;
  per_page?: number;
  filter?: { [key: string]: string };
  sort?: string;
}) => {
  const query = useProducts(params);
  
  return {
    ...query,
    data: query.data ? {
      ...query.data,
      data: transformProducts(query.data.data)
    } : undefined,
  };
};

export const useFeaturedProductsLegacy = () => {
  const query = useFeaturedProducts();

  return {
    ...query,
    data: query.data ? transformProducts(query.data.data) : undefined,
  };
};

// Catalog hooks
export const useCatalogs = (params?: {
  category?: string;
  search?: string;
}) => {
  return useQuery({
    queryKey: ['catalogs', params],
    queryFn: () => catalogApi.getCatalogs(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

export const useCatalog = (slug: string) => {
  return useQuery({
    queryKey: ['catalogs', slug],
    queryFn: () => catalogApi.getCatalog(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });
};

export const useCatalogCategories = () => {
  return useQuery({
    queryKey: ['catalog-categories'],
    queryFn: () => catalogApi.getCategories(),
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};
