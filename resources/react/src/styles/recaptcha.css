/* Custom reCAPTCHA Styling */

/* Container styling */
.recaptcha-container {
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

/* Hover effect for the container */
.recaptcha-container:hover {
  transform: scale(1.02) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

/* Custom styling for the reCAPTCHA iframe */
.recaptcha-container iframe {
  border-radius: 8px !important;
  border: 2px solid #e5e7eb !important;
  transition: border-color 0.3s ease !important;
}

/* Focus/hover state for iframe */
.recaptcha-container:hover iframe {
  border-color: #3b82f6 !important;
}

/* Loading state */
.recaptcha-loading {
  position: relative;
}

.recaptcha-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: recaptcha-spin 1s linear infinite;
  z-index: 10;
}

@keyframes recaptcha-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error state */
.recaptcha-error {
  border-color: #ef4444 !important;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2) !important;
}

.recaptcha-error iframe {
  border-color: #ef4444 !important;
}

/* Success state */
.recaptcha-success {
  border-color: #10b981 !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2) !important;
}

.recaptcha-success iframe {
  border-color: #10b981 !important;
}

/* Compact size adjustments */
.recaptcha-compact {
  transform: scale(0.9);
  transform-origin: center;
}

/* Dark theme support */
.dark .recaptcha-container iframe {
  border-color: #374151 !important;
}

.dark .recaptcha-container:hover iframe {
  border-color: #60a5fa !important;
}

/* Mobile responsive */
@media (max-width: 640px) {
  .recaptcha-container {
    transform: scale(0.85) !important;
    transform-origin: center !important;
  }
  
  .recaptcha-container:hover {
    transform: scale(0.87) !important;
  }
}

/* Custom medical theme colors */
.recaptcha-medical {
  border-radius: 12px !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  padding: 8px !important;
}

.recaptcha-medical iframe {
  border: 2px solid #cbd5e1 !important;
  border-radius: 8px !important;
  background: white !important;
}

.recaptcha-medical:hover iframe {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Pulse animation for attention */
.recaptcha-pulse {
  animation: recaptcha-pulse 2s infinite;
}

@keyframes recaptcha-pulse {
  0% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 2px 16px rgba(59, 130, 246, 0.2);
  }
  100% {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }
}
