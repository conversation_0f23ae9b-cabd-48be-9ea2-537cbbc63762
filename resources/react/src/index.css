
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import './styles/recaptcha.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 40% 8%;

    --card: 0 0% 100%;
    --card-foreground: 210 40% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 8%;

    --primary: 220 45% 25%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 220 45% 25%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 214 32% 91%;
    --accent-foreground: 220 91% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 220 91% 20%;

    --radius: 0.75rem;

    /* Modern color palette - Green & Navy theme (swapped) */
    --gradient-primary: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    --gradient-secondary: linear-gradient(135deg, #3b4d66 0%, #2c3e50 100%);
    --gradient-accent: linear-gradient(135deg, #3b4d66 0%, #2c3e50 100%);
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);

    /* Brand colors - Swapped */
    --navy-primary: #27ae60;
    --navy-secondary: #2ecc71;
    --green-primary: #3b4d66;
    --green-secondary: #2c3e50;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 220 91% 20%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 220 91% 20%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 220 91% 20%;
  }

  .dark {
    --background: 220 27% 6%;
    --foreground: 210 40% 98%;

    --card: 220 27% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 220 27% 8%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 220 91% 20%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 220 27% 8%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    scroll-behavior: none;
  }

  html {
    scroll-behavior: none;
  }
}

@layer components {
  /* Modern gradient backgrounds */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  /* Glassmorphism effects */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  /* Modern card styles */
  .modern-card {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transition: all 0.5s;
    border-radius: 1rem;
    transform: translateY(0);
  }

  .modern-card:hover {
    transform: translateY(-8px);
  }

  /* Custom Scrollbar - RELIFE Brand Colors */
  ::-webkit-scrollbar {
    width: 12px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #27ae60 0%, #3b4d66 100%);
    border-radius: 6px;
    border: 2px solid #f1f5f9;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2ecc71 0%, #2c3e50 100%);
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: #3b4d66 #f1f5f9;
  }

  /* Enhanced medical styles - Navy & Green theme */
  .medical-gradient {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .medical-card {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(229, 231, 235, 0.5);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-radius: 1rem;
    overflow: hidden;
    border-color: rgba(39, 174, 96, 0.1);
  }

  .medical-button {
    background: var(--gradient-accent);
    color: white;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    transition: all 0.3s;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: translateY(0);
  }

  .medical-button:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #4a5d7a 0%, #34495e 100%);
  }

  .medical-section {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  @media (min-width: 1024px) {
    .medical-section {
      padding-top: 7rem;
      padding-bottom: 7rem;
    }
  }

  /* Animation utilities */
  .fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  /* Scroll animations */
  .scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
  }

  .scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
