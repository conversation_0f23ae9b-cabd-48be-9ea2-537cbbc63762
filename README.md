# RELIFE Medical Technologies - Backend API

A comprehensive Laravel 11 backend API with Filament PHP v3 admin panel for RELIFE Medical Technologies website.

## Features

- **Laravel 11** - Latest Laravel framework
- **Filament PHP v3** - Modern admin panel
- **Laravel Sanctum** - API authentication
- **Spatie Packages** - Enhanced functionality
- **RESTful API** - Complete API for frontend consumption
- **Media Management** - File and image handling
- **Role-based Access** - User permissions system
- **Activity Logging** - Audit trails
- **Database Backups** - Automated backup system

## Prerequisites

- PHP 8.2 or higher
- Composer
- MySQL/PostgreSQL
- Node.js (for asset compilation)

## Installation

### 1. Clone or Create Project
```bash
composer create-project laravel/laravel relife-backend
cd relife-backend
```

### 2. Install Dependencies
```bash
# Core packages
composer require filament/filament:"^3.0"
composer require laravel/sanctum
composer require spatie/laravel-media-library
composer require spatie/laravel-sluggable
composer require spatie/laravel-query-builder
```

### 3. Environment Setup
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure your .env file with database credentials
```

### 4. Database Setup
```bash
# Publish migrations
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan vendor:publish --provider="Spatie\MediaLibrary\MediaLibraryServiceProvider" --tag="medialibrary-migrations"
php artisan vendor:publish --provider="Spatie\ActivityLog\ActivitylogServiceProvider" --tag="activitylog-migrations"

# Run migrations
php artisan migrate
```

## Production Deployment

### Standard Deployment (Recommended)

**Important**: This repository excludes the `vendor/` directory following Laravel best practices. Dependencies must be installed on the deployment server.

1. **Clone the repository:**
   ```bash
   git clone https://<EMAIL>/baraakhb/Relife/_git/Relife.git
   cd Relife
   ```

2. **Install PHP dependencies:**
   ```bash
   composer install --optimize-autoloader --no-dev
   ```

3. **Environment configuration:**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Configure your `.env` file:**
   ```env
   APP_NAME="Relife Medical"
   APP_ENV=production
   APP_DEBUG=false
   APP_URL=https://yourdomain.com

   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=relife_db
   DB_USERNAME=your_username
   DB_PASSWORD=your_password

   # CAPTCHA Configuration
   CAPTCHA_SITE_KEY=your_captcha_site_key
   CAPTCHA_SECRET_KEY=your_captcha_secret_key

   # Mail Configuration
   MAIL_MAILER=smtp
   MAIL_HOST=your_smtp_host
   MAIL_PORT=587
   MAIL_USERNAME=your_email
   MAIL_PASSWORD=your_password
   MAIL_ENCRYPTION=tls
   MAIL_FROM_ADDRESS=<EMAIL>
   MAIL_FROM_NAME="Relife Medical"
   ```

5. **Database setup:**
   ```bash
   php artisan migrate --force
   php artisan db:seed --force
   ```

6. **Storage and permissions:**
   ```bash
   php artisan storage:link
   chmod -R 755 storage bootstrap/cache
   chown -R www-data:www-data storage bootstrap/cache
   ```

7. **Create admin user:**
   ```bash
   php artisan make:filament-user
   ```

### Production Optimization

```bash
# Cache configuration and routes
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Optimize autoloader
composer dump-autoload --optimize
```

### 5. Filament Setup
```bash
# Install Filament
php artisan filament:install --panels

# Create admin user
php artisan make:filament-user
```

### 6. Storage Setup
```bash
# Create storage link
php artisan storage:link
```

### 7. Seed Database
```bash
# Run seeders
php artisan db:seed --class=CategorySeeder
php artisan db:seed --class=ProductSeeder
php artisan db:seed --class=CatalogSeeder
```

### 8. Start Development Server
```bash
php artisan serve
```

## API Endpoints

### Products
- `GET /api/v1/products` - List all products
- `GET /api/v1/products/featured` - Featured products
- `GET /api/v1/products/search?q=query` - Search products
- `GET /api/v1/products/{slug}` - Get product details

### Categories
- `GET /api/v1/categories` - List all categories
- `GET /api/v1/categories/{slug}` - Get category details
- `GET /api/v1/categories/{slug}/products` - Get category products

### Catalogs
- `GET /api/v1/catalogs` - List all catalogs
- `GET /api/v1/catalogs/categories` - Get catalog categories
- `GET /api/v1/catalogs/{slug}` - Get catalog details
- `GET /api/v1/catalogs/{slug}/download` - Download catalog

### Contact
- `POST /api/v1/contact` - Submit contact form

## Admin Panel

Access the admin panel at: `http://localhost:8000/admin`

### Available Resources
- **Categories** - Manage product categories
- **Products** - Manage prosthetic products
- **Catalogs** - Manage downloadable resources
- **Contact Submissions** - View contact form submissions
- **Users** - User management
- **Roles & Permissions** - Access control

## Configuration

### CORS Configuration
Update `config/cors.php` to allow your frontend domain:
```php
'allowed_origins' => [
    'http://localhost:3000', // React development server
    'https://yourdomain.com', // Production domain
],
```

### File Storage
Configure file storage in `config/filesystems.php` for production use.

### Email Configuration
Set up email configuration in `.env` for contact form notifications:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
```

## Admin User System

### Simplified Admin Access
- Single admin user with `is_admin` flag
- Email: <EMAIL>
- Default password: ReLife2024!Admin (change after first login)
- Direct Filament panel access without complex permissions

### Spatie Packages Integration

### Laravel Media Library
- File and image management
- Image conversions and thumbnails
- Media collections

### Laravel Sluggable
- Automatic slug generation for SEO-friendly URLs
- Used for products, categories, and catalogs

## Development

### Adding New Models
1. Create migration: `php artisan make:migration create_table_name`
2. Create model with traits: `php artisan make:model ModelName`
3. Create Filament resource: `php artisan make:filament-resource ModelName`
4. Add API controller: `php artisan make:controller Api/ModelNameController`
5. Add routes to `routes/api.php`

### Testing
```bash
# Run tests
php artisan test

# Run specific test
php artisan test --filter=ProductTest
```

## Deployment

### Production Setup
1. Set `APP_ENV=production` in `.env`
2. Configure database credentials
3. Set up file storage (S3, etc.)
4. Configure email settings
5. Set up SSL certificate
6. Configure web server (Nginx/Apache)

### Optimization
```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev
```

## Security

- API rate limiting configured
- CORS properly set up
- Sanctum for API authentication
- Input validation on all endpoints
- SQL injection protection
- XSS protection

## Support

For technical support or questions, contact the development team or refer to the Laravel and Filament documentation.

## License

This project is proprietary software for RELIFE Medical Technologies.
